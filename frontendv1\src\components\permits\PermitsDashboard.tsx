import React, { useState, useEffect } from "react";
import {
	<PERSON>lipboard<PERSON>he<PERSON>,
	Clock,
	AlertTriangle,
	CheckCircle,
	Plus,
	TrendingUp,
} from "lucide-react";
import { PermitStats, Permit } from "../../types/permits";
import PermitStatusBadge from "./shared/PermitStatusBadge";
import PermitPriorityBadge from "./shared/PermitPriorityBadge";
import PermitCreationModal from "./PermitCreationModal";

interface PermitsDashboardProps {
	siteId: string;
	onNavigateToTab: (tabId: string) => void;
}

// Mock data - replace with actual API calls
const mockPermitStats: PermitStats = {
	totalPermits: 156,
	activePermits: 8,
	pendingApproval: 3,
	expiringSoon: 2,
	expired: 0,
	closedToday: 5,
	averageApprovalTime: 2.5,
	complianceRate: 98.5,
};

const mockRecentPermits: Permit[] = [
	{
		id: "permit-1",
		permitNumber: "HW-2024-001",
		permitType: {
			id: "hot-work",
			name: "Hot Work Permit",
			description: "For welding, cutting, and other hot work activities",
			category: "High Risk",
			defaultValidityHours: 8,
			requiredTrainings: ["hot-work-safety"],
			requiredCertifications: ["welding-cert"],
			riskLevel: "high",
			template: {} as any,
			isActive: true,
			createdAt: new Date(),
			updatedAt: new Date(),
		},
		title: "Structural Steel Welding - Level 3",
		description: "Welding operations for structural steel installation",
		location: "Zone A - Level 3",
		siteId: "site-1",
		requestedDate: new Date("2024-01-15T08:00:00"),
		validFrom: new Date("2024-01-15T09:00:00"),
		validUntil: new Date("2024-01-15T17:00:00"),
		status: "active",
		priority: "high",
		requestedBy: "supervisor-1",
		requestedByName: "John Smith",
		assignedWorkers: [],
		approvals: [],
		riskAssessment: {} as any,
		dailyRiskAssessments: [],
		formData: {},
		attachments: [],
		createdAt: new Date("2024-01-15T07:30:00"),
		updatedAt: new Date("2024-01-15T09:00:00"),
		history: [],
	},
	{
		id: "permit-2",
		permitNumber: "CS-2024-002",
		permitType: {
			id: "confined-space",
			name: "Confined Space Entry",
			description: "For entry into confined spaces",
			category: "High Risk",
			defaultValidityHours: 4,
			requiredTrainings: ["confined-space-entry"],
			requiredCertifications: ["gas-monitor-cert"],
			riskLevel: "critical",
			template: {} as any,
			isActive: true,
			createdAt: new Date(),
			updatedAt: new Date(),
		},
		title: "Tank Inspection - Storage Tank 3",
		description: "Internal inspection of water storage tank",
		location: "Zone B - Tank Farm",
		siteId: "site-1",
		requestedDate: new Date("2024-01-15T10:00:00"),
		status: "pending-approval",
		priority: "critical",
		requestedBy: "supervisor-2",
		requestedByName: "Sarah Johnson",
		assignedWorkers: [],
		approvals: [],
		riskAssessment: {} as any,
		dailyRiskAssessments: [],
		formData: {},
		attachments: [],
		createdAt: new Date("2024-01-15T09:45:00"),
		updatedAt: new Date("2024-01-15T09:45:00"),
		history: [],
	},
];

const PermitsDashboard: React.FC<PermitsDashboardProps> = ({
	siteId,
	onNavigateToTab,
}) => {
	const [stats, _setStats] = useState<PermitStats>(mockPermitStats);
	const [recentPermits, setRecentPermits] =
		useState<Permit[]>(mockRecentPermits);
	const [isPermitModalOpen, setIsPermitModalOpen] = useState(false);

	useEffect(() => {
		// Fetch permit statistics and recent permits
		// This would be replaced with actual API calls
		console.log(`Fetching permit data for site ${siteId}`);
	}, [siteId]);

	const handleCreatePermit = (permitData: any) => {
		// Here you would call the actual API to create the permit
		console.log('Creating permit:', permitData);

		// For now, just add to local state
		const newPermit: Permit = {
			id: `permit-${Date.now()}`,
			permitNumber: `PRM-${new Date().getFullYear()}-${String(recentPermits.length + 1).padStart(3, '0')}`,
			title: permitData.title,
			description: permitData.description,
			location: permitData.location,
			siteId: permitData.siteId,
			status: 'draft',
			priority: permitData.priority,
			requestedDate: new Date(),
			requestedBy: 'current-user',
			requestedByName: 'Current User',
			permitType: {
				id: 'custom',
				name: permitData.selectedTaskTemplate?.name || 'Custom Permit',
				description: permitData.selectedTaskTemplate?.description || '',
				category: 'Custom',
				defaultValidityHours: permitData.selectedTaskTemplate?.estimatedDuration || 8,
				requiredTrainings: permitData.selectedTaskTemplate?.requiredTrainings || [],
				requiredCertifications: permitData.selectedTaskTemplate?.requiredCertifications || [],
				riskLevel: permitData.selectedTaskTemplate?.riskLevel || 'medium',
				template: {} as any,
				isActive: true,
				createdAt: new Date(),
				updatedAt: new Date(),
			},
			assignedWorkers: [],
			approvals: [],
			riskAssessment: {} as any,
			dailyRiskAssessments: [],
			formData: {},
			attachments: [],
			createdAt: new Date(),
			updatedAt: new Date(),
			history: []
		};

		setRecentPermits(prev => [newPermit, ...prev]);
		setIsPermitModalOpen(false);
	};

	return (
		<div className="space-y-6">
			{/* Key Metrics */}
			<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
				<div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
					<div className="flex justify-between items-start">
						<div>
							<p className="text-sm font-medium text-gray-500">
								Active Permits
							</p>
							<p className="mt-2 text-2xl font-semibold text-gray-900">
								{stats.activePermits}
							</p>
						</div>
						<div className="text-green-600">
							<ClipboardCheck className="h-6 w-6" />
						</div>
					</div>
				</div>

				<div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
					<div className="flex justify-between items-start">
						<div>
							<p className="text-sm font-medium text-gray-500">
								Pending Approval
							</p>
							<p className="mt-2 text-2xl font-semibold text-gray-900">
								{stats.pendingApproval}
							</p>
						</div>
						<div className="text-amber-500">
							<Clock className="h-6 w-6" />
						</div>
					</div>
				</div>

				<div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
					<div className="flex justify-between items-start">
						<div>
							<p className="text-sm font-medium text-gray-500">Expiring Soon</p>
							<p className="mt-2 text-2xl font-semibold text-gray-900">
								{stats.expiringSoon}
							</p>
						</div>
						<div className="text-orange-500">
							<AlertTriangle className="h-6 w-6" />
						</div>
					</div>
				</div>

				<div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
					<div className="flex justify-between items-start">
						<div>
							<p className="text-sm font-medium text-gray-500">
								Compliance Rate
							</p>
							<p className="mt-2 text-2xl font-semibold text-gray-900">
								{stats.complianceRate}%
							</p>
						</div>
						<div className="text-green-600">
							<CheckCircle className="h-6 w-6" />
						</div>
					</div>
				</div>
			</div>

			{/* Quick Actions */}
			<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
				<button
					onClick={() => onNavigateToTab("all-permits")}
					className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow text-left"
				>
					<div className="flex items-center">
						<div className="flex-shrink-0">
							<Plus className="h-8 w-8 text-green-600" />
						</div>
						<div className="ml-4">
							<h3 className="text-lg font-medium text-gray-900">
								Create New Permit
							</h3>
							<p className="text-sm text-gray-500">
								Start a new work permit application
							</p>
						</div>
					</div>
				</button>

				<button
					onClick={() => onNavigateToTab("active-permits")}
					className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow text-left"
				>
					<div className="flex items-center">
						<div className="flex-shrink-0">
							<ClipboardCheck className="h-8 w-8 text-blue-600" />
						</div>
						<div className="ml-4">
							<h3 className="text-lg font-medium text-gray-900">
								View Active Permits
							</h3>
							<p className="text-sm text-gray-500">
								Manage current work permits
							</p>
						</div>
					</div>
				</button>

				<button
					onClick={() => onNavigateToTab("reports")}
					className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow text-left"
				>
					<div className="flex items-center">
						<div className="flex-shrink-0">
							<TrendingUp className="h-8 w-8 text-purple-600" />
						</div>
						<div className="ml-4">
							<h3 className="text-lg font-medium text-gray-900">
								View Reports
							</h3>
							<p className="text-sm text-gray-500">
								Analyze permit trends and compliance
							</p>
						</div>
					</div>
				</button>
			</div>

			{/* Recent Permits */}
			<div className="bg-white rounded-lg border border-gray-200 shadow-sm">
				<div className="px-6 py-4 border-b border-gray-200">
					<div className="flex justify-between items-center">
						<h3 className="text-lg font-medium text-gray-900">
							Recent Permits
						</h3>
						<button
							onClick={() => onNavigateToTab("active-permits")}
							className="text-sm text-green-600 hover:text-green-800"
						>
							View All
						</button>
					</div>
				</div>
				<div className="divide-y divide-gray-200">
					{recentPermits.map((permit) => (
						<div key={permit.id} className="px-6 py-4 hover:bg-gray-50">
							<div className="flex justify-between items-start">
								<div className="flex-1">
									<div className="flex items-center space-x-3">
										<h4 className="text-sm font-medium text-gray-900">
											{permit.title}
										</h4>
										<PermitStatusBadge status={permit.status} size="sm" />
										<PermitPriorityBadge priority={permit.priority} size="sm" />
									</div>
									<p className="text-sm text-gray-500 mt-1">
										{permit.permitType.name} • {permit.location}
									</p>
									<p className="text-xs text-gray-400 mt-1">
										Requested by {permit.requestedByName} •{" "}
										{permit.permitNumber}
									</p>
								</div>
								<div className="text-right">
									<p className="text-sm text-gray-500">
										{permit.validFrom && permit.validUntil
											? `${permit.validFrom.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })} - ${permit.validUntil.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })}`
											: "Pending approval"}
									</p>
								</div>
							</div>
						</div>
					))}
				</div>
			</div>

			{/* Permit Creation Modal */}
			<PermitCreationModal
				isOpen={isPermitModalOpen}
				onClose={() => setIsPermitModalOpen(false)}
				onSubmit={handleCreatePermit}
				siteId={siteId}
			/>
		</div>
	);
};

export default PermitsDashboard;
