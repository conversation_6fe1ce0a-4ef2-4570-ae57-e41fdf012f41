import React, { useState } from 'react';
import ApprovePermitComponent from '../../components/permits/ApprovePermitComponent';
import { Folder } from '../../types/approvePermit';

const ApprovePermitPage: React.FC = () => {
  const [selectedFile, setSelectedFile] = useState<{ id: string; path: string } | null>(null);

  // Generate random string for IDs
  const generateId = () => Math.random().toString(36).substr(2, 9);

  // Generate random file names
  const getRandomFileName = () => {
    const fileNames = [
      'signature.pdf', 'certificate.pdf', 'training_record.pdf', 'safety_checklist.pdf',
      'risk_assessment.pdf', 'equipment_check.pdf', 'photo_evidence.jpg', 'inspection_report.pdf',
      'compliance_doc.pdf', 'authorization.pdf'
    ];
    return fileNames[Math.floor(Math.random() * fileNames.length)];
  };

  // Generate files with random count (up to max)
  const generateFiles = (maxCount: number) => {
    const count = Math.floor(Math.random() * maxCount) + 1;
    return Array.from({ length: count }, () => ({
      id: generateId(),
      name: getRandomFileName()
    }));
  };

  // Dummy data following the specified structure
  const dummyFolders: Folder[] = [
    {
      id: generateId(),
      name: "Hot Work Permit",
      files: [
        {
          id: generateId(),
          name: "permit"
        }
      ],
      folders: [
        {
          id: generateId(),
          name: "workers",
          files: [],
          folders: [
            {
              id: generateId(),
              name: "Permit Issue",
              files: [],
              folders: [
                {
                  id: generateId(),
                  name: "Competent person",
                  files: generateFiles(2),
                  folders: []
                },
                {
                  id: generateId(),
                  name: "Authorizing person",
                  files: generateFiles(2),
                  folders: []
                }
              ]
            },
            {
              id: generateId(),
              name: "Permit Return",
              files: [],
              folders: [
                {
                  id: generateId(),
                  name: "Competent person",
                  files: generateFiles(2),
                  folders: []
                },
                {
                  id: generateId(),
                  name: "Authorizing person",
                  files: generateFiles(2),
                  folders: []
                }
              ]
            },
            {
              id: generateId(),
              name: "Sign off",
              files: generateFiles(10),
              folders: []
            }
          ]
        },
        {
          id: generateId(),
          name: "docs",
          files: generateFiles(3),
          folders: []
        }
      ]
    },
    {
      id: generateId(),
      name: "Confined Space Entry Permit",
      files: [
        {
          id: generateId(),
          name: "permit"
        }
      ],
      folders: [
        {
          id: generateId(),
          name: "workers",
          files: [],
          folders: [
            {
              id: generateId(),
              name: "Permit Issue",
              files: [],
              folders: [
                {
                  id: generateId(),
                  name: "Competent person",
                  files: generateFiles(2),
                  folders: []
                },
                {
                  id: generateId(),
                  name: "Authorizing person",
                  files: generateFiles(2),
                  folders: []
                }
              ]
            },
            {
              id: generateId(),
              name: "Permit Return",
              files: [],
              folders: [
                {
                  id: generateId(),
                  name: "Competent person",
                  files: generateFiles(2),
                  folders: []
                },
                {
                  id: generateId(),
                  name: "Authorizing person",
                  files: generateFiles(2),
                  folders: []
                }
              ]
            },
            {
              id: generateId(),
              name: "Sign off",
              files: generateFiles(10),
              folders: []
            }
          ]
        },
        {
          id: generateId(),
          name: "docs",
          files: generateFiles(3),
          folders: []
        }
      ]
    },
    {
      id: generateId(),
      name: "Work at Height Permit",
      files: [
        {
          id: generateId(),
          name: "permit"
        }
      ],
      folders: [
        {
          id: generateId(),
          name: "workers",
          files: [],
          folders: [
            {
              id: generateId(),
              name: "Permit Issue",
              files: [],
              folders: [
                {
                  id: generateId(),
                  name: "Competent person",
                  files: generateFiles(2),
                  folders: []
                },
                {
                  id: generateId(),
                  name: "Authorizing person",
                  files: generateFiles(2),
                  folders: []
                }
              ]
            },
            {
              id: generateId(),
              name: "Permit Return",
              files: [],
              folders: [
                {
                  id: generateId(),
                  name: "Competent person",
                  files: generateFiles(2),
                  folders: []
                },
                {
                  id: generateId(),
                  name: "Authorizing person",
                  files: generateFiles(2),
                  folders: []
                }
              ]
            },
            {
              id: generateId(),
              name: "Sign off",
              files: generateFiles(10),
              folders: []
            }
          ]
        },
        {
          id: generateId(),
          name: "docs",
          files: generateFiles(3),
          folders: []
        }
      ]
    }
  ];

  const handleFileClick = (fileId: string, filePath: string) => {
    setSelectedFile({ id: fileId, path: filePath });
    console.log('File clicked:', { fileId, filePath });
  };

  const documentContent = selectedFile ? (
    <div className="space-y-4">
      <div className="bg-gray-100 p-4 rounded-lg">
        <h3 className="font-semibold text-gray-900 mb-2">File Information</h3>
        <p className="text-sm text-gray-600"><strong>File ID:</strong> {selectedFile.id}</p>
        <p className="text-sm text-gray-600"><strong>File Path:</strong> {selectedFile.path}</p>
      </div>
      <div className="bg-white border border-gray-200 rounded-lg p-8 text-center">
        <p className="text-gray-500 mb-4">Document preview would appear here</p>
        <div className="bg-gray-50 h-96 flex items-center justify-center rounded border-2 border-dashed border-gray-300">
          <span className="text-gray-400">Document Content</span>
        </div>
      </div>
    </div>
  ) : undefined;

  const extraDetailsContent = (
    <div className="space-y-4">
      <div className="bg-blue-50 p-4 rounded-lg">
        <h4 className="font-semibold text-blue-900 mb-2">Permit Status</h4>
        <p className="text-sm text-blue-700">Pending Approval</p>
      </div>
      <div className="bg-gray-50 p-4 rounded-lg">
        <h4 className="font-semibold text-gray-900 mb-2">Submitted By</h4>
        <p className="text-sm text-gray-600">John Smith</p>
        <p className="text-xs text-gray-500">Site Supervisor</p>
      </div>
      <div className="bg-gray-50 p-4 rounded-lg">
        <h4 className="font-semibold text-gray-900 mb-2">Submission Date</h4>
        <p className="text-sm text-gray-600">{new Date().toLocaleDateString()}</p>
      </div>
    </div>
  );

  return (
    <ApprovePermitComponent
      folders={dummyFolders}
      showButtons={true}
      onFileClick={handleFileClick}
      documentTitle={selectedFile ? `Viewing: ${selectedFile.path}` : "Document Viewer"}
      documentChildren={documentContent}
      extraDetailsChildren={extraDetailsContent}
    />
  );
};

export default ApprovePermitPage;
