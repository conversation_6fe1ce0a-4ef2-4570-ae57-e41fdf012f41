import React, { useState, useEffect, useRef } from "react";
import {
	Search,
	Plus,
	Eye,
	Edit,
	Clock,
	Users,
	MapPin,
	ClipboardCheck,
	X,
	XCircle,
	RotateCcw,
	ChevronDown,
} from "lucide-react";
import { Permit, PermitFilters } from "../../types/permits";
import PermitStatusBadge from "./shared/PermitStatusBadge";
import PermitPriorityBadge from "./shared/PermitPriorityBadge";
import PermitCreationModal from "./PermitCreationModal";

interface ActivePermitsProps {
	siteId: string;
}

// Mock data - replace with actual API calls
const mockActivePermits: Permit[] = [
	{
		id: "permit-1",
		permitNumber: "HW-2024-001",
		permitType: {
			id: "hot-work",
			name: "Hot Work Permit",
			description: "For welding, cutting, and other hot work activities",
			category: "High Risk",
			defaultValidityHours: 8,
			requiredTrainings: ["hot-work-safety"],
			requiredCertifications: ["welding-cert"],
			riskLevel: "high",
			template: {} as any,
			isActive: true,
			createdAt: new Date(),
			updatedAt: new Date(),
		},
		title: "Structural Steel Welding - Level 3",
		description: "Welding operations for structural steel installation",
		location: "Zone A - Level 3",
		siteId: "site-1",
		requestedDate: new Date("2024-01-15T08:00:00"),
		validFrom: new Date("2024-01-15T09:00:00"),
		validUntil: new Date("2024-01-15T17:00:00"),
		status: "active",
		priority: "high",
		requestedBy: "supervisor-1",
		requestedByName: "John Smith",
		assignedWorkers: [
			{
				workerId: "worker-1",
				workerName: "Mike Johnson",
				primaryTrade: "Welder",
				role: "worker",
				hasRequiredTraining: true,
				hasRequiredCertifications: true,
				acknowledgedAt: new Date("2024-01-15T08:45:00"),
			},
			{
				workerId: "worker-2",
				workerName: "David Wilson",
				primaryTrade: "Welder",
				role: "supervisor",
				hasRequiredTraining: true,
				hasRequiredCertifications: true,
				acknowledgedAt: new Date("2024-01-15T08:45:00"),
			},
		],
		approvals: [],
		riskAssessment: {} as any,
		dailyRiskAssessments: [],
		formData: {},
		attachments: [],
		createdAt: new Date("2024-01-15T07:30:00"),
		updatedAt: new Date("2024-01-15T09:00:00"),
		history: [],
	},
	{
		id: "permit-2",
		permitNumber: "CS-2024-002",
		permitType: {
			id: "confined-space",
			name: "Confined Space Entry",
			description: "For entry into confined spaces",
			category: "High Risk",
			defaultValidityHours: 4,
			requiredTrainings: ["confined-space-entry"],
			requiredCertifications: ["gas-monitor-cert"],
			riskLevel: "critical",
			template: {} as any,
			isActive: true,
			createdAt: new Date(),
			updatedAt: new Date(),
		},
		title: "Tank Inspection - Storage Tank 3",
		description: "Internal inspection of water storage tank",
		location: "Zone B - Tank Farm",
		siteId: "site-1",
		requestedDate: new Date("2024-01-15T10:00:00"),
		status: "pending-approval",
		priority: "critical",
		requestedBy: "supervisor-2",
		requestedByName: "Sarah Johnson",
		assignedWorkers: [
			{
				workerId: "worker-3",
				workerName: "Robert Brown",
				primaryTrade: "Inspector",
				role: "worker",
				hasRequiredTraining: true,
				hasRequiredCertifications: true,
			},
		],
		approvals: [],
		riskAssessment: {} as any,
		dailyRiskAssessments: [],
		formData: {},
		attachments: [],
		createdAt: new Date("2024-01-15T09:45:00"),
		updatedAt: new Date("2024-01-15T09:45:00"),
		history: [],
	},
];

const ActivePermits: React.FC<ActivePermitsProps> = ({ siteId }) => {
	const [permits, setPermits] = useState<Permit[]>(mockActivePermits);
	const [filteredPermits, setFilteredPermits] =
		useState<Permit[]>(mockActivePermits);
	const [isPermitModalOpen, setIsPermitModalOpen] = useState(false);
	const [isCancelModalOpen, setIsCancelModalOpen] = useState(false);
	const [isReturnModalOpen, setIsReturnModalOpen] = useState(false);
	const [selectedPermit, setSelectedPermit] = useState<Permit | null>(null);
	const [cancelReason, setCancelReason] = useState("");
	const [returnFormData, setReturnFormData] = useState<any>({});
	const [selectedNotifyUsers, setSelectedNotifyUsers] = useState<string[]>([]);
	const [isNotifyDropdownOpen, setIsNotifyDropdownOpen] = useState(false);
	const dropdownRef = useRef<HTMLDivElement>(null);
	const [filters, setFilters] = useState<PermitFilters>({
		search: "",
		status: "all",
		permitType: "",
		priority: "all",
		assignedWorker: "",
		dateRange: {},
		location: "",
	});

	useEffect(() => {
		// Fetch active permits for the site
		console.log(`Fetching active permits for site ${siteId}`);
	}, [siteId]);

	// Close dropdown when clicking outside
	useEffect(() => {
		const handleClickOutside = (event: MouseEvent) => {
			if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
				setIsNotifyDropdownOpen(false);
			}
		};

		document.addEventListener('mousedown', handleClickOutside);
		return () => {
			document.removeEventListener('mousedown', handleClickOutside);
		};
	}, []);

	const handleCreatePermit = (permitData: any) => {
		// Here you would call the actual API to create the permit
		console.log('Creating permit:', permitData);

		// For now, just add to local state
		const newPermit: Permit = {
			id: `permit-${Date.now()}`,
			permitNumber: `PRM-${new Date().getFullYear()}-${String(permits.length + 1).padStart(3, '0')}`,
			title: permitData.title,
			description: permitData.description,
			location: permitData.location,
			siteId: permitData.siteId,
			status: 'draft',
			priority: permitData.priority,
			requestedDate: new Date(),
			requestedBy: 'current-user',
			requestedByName: 'Current User',
			permitType: {
				id: 'custom',
				name: permitData.selectedTaskTemplate?.name || 'Custom Permit',
				description: permitData.selectedTaskTemplate?.description || '',
				category: 'Custom',
				defaultValidityHours: permitData.selectedTaskTemplate?.estimatedDuration || 8,
				requiredTrainings: permitData.selectedTaskTemplate?.requiredTrainings || [],
				requiredCertifications: permitData.selectedTaskTemplate?.requiredCertifications || [],
				riskLevel: permitData.selectedTaskTemplate?.riskLevel || 'medium',
				template: {} as any,
				isActive: true,
				createdAt: new Date(),
				updatedAt: new Date(),
			},
			assignedWorkers: [],
			approvals: [],
			riskAssessment: {} as any,
			dailyRiskAssessments: [],
			formData: {},
			attachments: [],
			createdAt: new Date(),
			updatedAt: new Date(),
			history: []
		};

		setPermits(prev => [newPermit, ...prev]);
		setIsPermitModalOpen(false);
	};

	const handleCancelPermit = (permit: Permit) => {
		setSelectedPermit(permit);
		setIsCancelModalOpen(true);
	};

	const handleReturnPermit = (permit: Permit) => {
		setSelectedPermit(permit);
		setIsReturnModalOpen(true);
	};

	const handleConfirmCancel = () => {
		if (selectedPermit && cancelReason.trim()) {
			console.log('Cancelling permit:', selectedPermit.id, 'Reason:', cancelReason);
			// Here you would call the API to cancel the permit
			setIsCancelModalOpen(false);
			setSelectedPermit(null);
			setCancelReason("");
		}
	};

	const handleConfirmReturn = () => {
		if (selectedPermit) {
			console.log('Returning permit:', selectedPermit.id, 'Form data:', returnFormData, 'Notify users:', selectedNotifyUsers);
			// Here you would call the API to return the permit
			setIsReturnModalOpen(false);
			setSelectedPermit(null);
			setReturnFormData({});
			setSelectedNotifyUsers([]);
			setIsNotifyDropdownOpen(false);
		}
	};

	const handleReturnFormChange = (fieldKey: string, value: any) => {
		setReturnFormData(prev => ({
			...prev,
			[fieldKey]: value
		}));
	};

	const availableUsers = [
		{ id: "supervisor1", name: "John Smith", role: "Site Supervisor" },
		{ id: "safety1", name: "Sarah Johnson", role: "Safety Officer" },
		{ id: "manager1", name: "Mike Wilson", role: "Project Manager" },
		{ id: "engineer1", name: "Lisa Brown", role: "Site Engineer" },
		{ id: "coordinator1", name: "David Lee", role: "Safety Coordinator" },
	];

	const handleUserToggle = (userId: string) => {
		setSelectedNotifyUsers(prev =>
			prev.includes(userId)
				? prev.filter(id => id !== userId)
				: [...prev, userId]
		);
	};

	useEffect(() => {
		// Apply filters
		let filtered = permits;

		if (filters.search) {
			filtered = filtered.filter(
				(permit) =>
					permit.title.toLowerCase().includes(filters.search.toLowerCase()) ||
					permit.permitNumber
						.toLowerCase()
						.includes(filters.search.toLowerCase()) ||
					permit.description
						.toLowerCase()
						.includes(filters.search.toLowerCase()),
			);
		}

		if (filters.status !== "all") {
			filtered = filtered.filter((permit) => permit.status === filters.status);
		}

		if (filters.priority !== "all") {
			filtered = filtered.filter(
				(permit) => permit.priority === filters.priority,
			);
		}

		if (filters.location) {
			filtered = filtered.filter((permit) =>
				permit.location.toLowerCase().includes(filters.location.toLowerCase()),
			);
		}

		setFilteredPermits(filtered);
	}, [permits, filters]);

	const getRemainingTime = (validUntil: Date) => {
		const now = new Date();
		const diff = validUntil.getTime() - now.getTime();
		const hours = Math.floor(diff / (1000 * 60 * 60));
		const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

		if (diff <= 0) return "Expired";
		if (hours < 1) return `${minutes}m remaining`;
		return `${hours}h ${minutes}m remaining`;
	};

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="flex justify-between items-center">
				<h2 className="text-2xl font-bold text-gray-900">Active Permits</h2>
				<button
					onClick={() => setIsPermitModalOpen(true)}
					className="flex items-center px-4 py-2 bg-green-600 text-white rounded-md text-sm font-medium hover:bg-green-700"
				>
					<Plus className="h-4 w-4 mr-2" />
					Create New Permit
				</button>
			</div>

			{/* Filters */}
			<div className="bg-white rounded-lg border border-gray-200 p-4">
				<div className="grid grid-cols-1 md:grid-cols-4 gap-4">
					<div className="relative">
						<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
						<input
							type="text"
							placeholder="Search permits..."
							value={filters.search}
							onChange={(e) =>
								setFilters({ ...filters, search: e.target.value })
							}
							className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
						/>
					</div>

					<select
						value={filters.status}
						onChange={(e) =>
							setFilters({ ...filters, status: e.target.value as any })
						}
						className="px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
					>
						<option value="all">All Statuses</option>
						<option value="active">Active</option>
						<option value="pending-approval">Pending Approval</option>
						<option value="expired">Expired</option>
					</select>

					<select
						value={filters.priority}
						onChange={(e) =>
							setFilters({ ...filters, priority: e.target.value as any })
						}
						className="px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
					>
						<option value="all">All Priorities</option>
						<option value="critical">Critical</option>
						<option value="high">High</option>
						<option value="medium">Medium</option>
						<option value="low">Low</option>
					</select>

					<input
						type="text"
						placeholder="Filter by location..."
						value={filters.location}
						onChange={(e) =>
							setFilters({ ...filters, location: e.target.value })
						}
						className="px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
					/>
				</div>
			</div>

			{/* Permits List */}
			<div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
				<div className="divide-y divide-gray-200">
					{filteredPermits.map((permit) => (
						<div key={permit.id} className="p-6 hover:bg-gray-50">
							<div className="flex justify-between items-start">
								<div className="flex-1">
									<div className="flex items-center space-x-3 mb-2">
										<h3 className="text-lg font-medium text-gray-900">
											{permit.title}
										</h3>
										<PermitStatusBadge status={permit.status} size="sm" />
										<PermitPriorityBadge priority={permit.priority} size="sm" />
									</div>

									<p className="text-sm text-gray-600 mb-3">
										{permit.description}
									</p>

									<div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-500">
										<div className="flex items-center">
											<MapPin className="h-4 w-4 mr-1" />
											{permit.location}
										</div>
										<div className="flex items-center">
											<Users className="h-4 w-4 mr-1" />
											{permit.assignedWorkers.length} worker(s) assigned
										</div>
										<div className="flex items-center">
											<Clock className="h-4 w-4 mr-1" />
											{permit.validUntil
												? getRemainingTime(permit.validUntil)
												: "No expiry set"}
										</div>
									</div>

									<div className="mt-3 text-xs text-gray-400">
										{permit.permitType.name} • {permit.permitNumber} • Requested
										by {permit.requestedByName}
									</div>
								</div>

								<div className="flex space-x-2 ml-4">
									<button className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-md">
										<Eye className="h-4 w-4" />
									</button>
									<button className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-md">
										<Edit className="h-4 w-4" />
									</button>
									<button
										onClick={(e) => {
											e.stopPropagation();
											handleReturnPermit(permit);
										}}
										className="px-3 py-1 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
									>
										Permit Return
									</button>
									<button
										onClick={(e) => {
											e.stopPropagation();
											handleCancelPermit(permit);
										}}
										className="px-3 py-1 text-sm bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
									>
										Cancel
									</button>
								</div>
							</div>
						</div>
					))}
				</div>

				{filteredPermits.length === 0 && (
					<div className="p-12 text-center">
						<ClipboardCheck className="h-12 w-12 text-gray-400 mx-auto mb-4" />
						<h3 className="text-lg font-medium text-gray-900 mb-2">
							No permits found
						</h3>
						<p className="text-gray-500">
							{filters.search ||
							filters.status !== "all" ||
							filters.priority !== "all" ||
							filters.location
								? "Try adjusting your filters to see more results."
								: "Create your first permit to get started."}
						</p>
					</div>
				)}
			</div>

			{/* Permit Creation Modal */}
			<PermitCreationModal
				isOpen={isPermitModalOpen}
				onClose={() => setIsPermitModalOpen(false)}
				onSubmit={handleCreatePermit}
				siteId={siteId}
			/>

			{/* Cancel Permit Modal */}
			{isCancelModalOpen && (
				<div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
					<div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
						<div className="flex justify-between items-center p-6 border-b border-gray-200">
							<h2 className="text-xl font-semibold text-gray-900">Cancel Permit</h2>
							<button
								onClick={() => {
									setIsCancelModalOpen(false);
									setSelectedPermit(null);
									setCancelReason("");
								}}
								className="text-gray-400 hover:text-gray-600 transition-colors"
							>
								<X className="h-6 w-6" />
							</button>
						</div>

						<div className="p-6">
							<div className="mb-4">
								<p className="text-sm text-gray-600 mb-2">
									You are about to cancel permit: <strong>{selectedPermit?.title}</strong>
								</p>
								<p className="text-sm text-gray-600 mb-4">
									Permit Number: <strong>{selectedPermit?.permitNumber}</strong>
								</p>
							</div>

							<div className="mb-6">
								<label className="block text-sm font-medium text-gray-700 mb-2">
									Reason for cancellation *
								</label>
								<textarea
									value={cancelReason}
									onChange={(e) => setCancelReason(e.target.value)}
									placeholder="Please provide a reason for cancelling this permit..."
									className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-transparent resize-none"
									rows={4}
									required
								/>
							</div>

							<div className="flex space-x-3">
								<button
									onClick={() => {
										setIsCancelModalOpen(false);
										setSelectedPermit(null);
										setCancelReason("");
									}}
									className="flex-1 px-4 py-2 bg-green-600 text-white rounded-md text-sm font-medium hover:bg-green-700 transition-colors"
								>
									Back
								</button>
								<button
									onClick={handleConfirmCancel}
									disabled={!cancelReason.trim()}
									className="flex-1 px-4 py-2 bg-red-600 text-white rounded-md text-sm font-medium hover:bg-red-700 transition-colors disabled:bg-gray-300 disabled:cursor-not-allowed"
								>
									Cancel Permit
								</button>
							</div>
						</div>
					</div>
				</div>
			)}

			{/* Return Permit Modal */}
			{isReturnModalOpen && (
				<div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
					<div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
						<div className="flex justify-between items-center p-6 border-b border-gray-200">
							<h2 className="text-xl font-semibold text-gray-900">Return Permit</h2>
							<button
								onClick={() => {
									setIsReturnModalOpen(false);
									setSelectedPermit(null);
									setReturnFormData({});
									setSelectedNotifyUsers([]);
									setIsNotifyDropdownOpen(false);
								}}
								className="text-gray-400 hover:text-gray-600 transition-colors"
							>
								<X className="h-6 w-6" />
							</button>
						</div>

						<div className="p-6 space-y-6">
							{/* Permit Return Section */}
							<div className="bg-white border border-gray-200 rounded-lg p-4">
								<h3 className="text-lg font-semibold text-gray-900 mb-3">Permit Return</h3>
								<p className="text-sm text-gray-600 mb-4">
									I accept that this work has been completed satisfactorily, the personnel and the equipment have been inspected and tagged as appropriate. The work area has been properly housekept.
								</p>

								<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
									{/* Competent Person (Permit Receiver) */}
									<div className="border border-gray-200 rounded p-3">
										<h4 className="text-sm font-medium text-gray-800 mb-2">Competent Person (Permit Receiver)</h4>
										<div className="space-y-2">
											<div>
												<label className="block text-xs font-medium text-gray-700 mb-1">Name *</label>
												<input
													type="text"
													value={returnFormData['Permit Return_Competent Person (Permit Receiver)_Name'] || ''}
													onChange={(e) => handleReturnFormChange('Permit Return_Competent Person (Permit Receiver)_Name', e.target.value)}
													className="w-full px-2 py-1 text-sm border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
													required
												/>
											</div>
											<div>
												<label className="block text-xs font-medium text-gray-700 mb-1">Date *</label>
												<input
													type="date"
													value={returnFormData['Permit Return_Competent Person (Permit Receiver)_Date'] || ''}
													onChange={(e) => handleReturnFormChange('Permit Return_Competent Person (Permit Receiver)_Date', e.target.value)}
													className="w-full px-2 py-1 text-sm border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
													required
												/>
											</div>
											<div>
												<label className="block text-xs font-medium text-gray-700 mb-1">Time *</label>
												<input
													type="time"
													value={returnFormData['Permit Return_Competent Person (Permit Receiver)_Time'] || ''}
													onChange={(e) => handleReturnFormChange('Permit Return_Competent Person (Permit Receiver)_Time', e.target.value)}
													className="w-full px-2 py-1 text-sm border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
													required
												/>
											</div>
											<div>
												<label className="block text-xs font-medium text-gray-700 mb-1">Signature *</label>
												<div className="border border-gray-300 rounded-md p-2 text-center text-xs bg-gray-50 text-gray-500">
													Signature Pad
												</div>
											</div>
										</div>
									</div>

									{/* Authorizing Person (Permit Issuer) */}
									<div className="border border-gray-200 rounded p-3">
										<h4 className="text-sm font-medium text-gray-800 mb-2">Authorizing Person (Permit Issuer)</h4>
										<div className="space-y-2">
											<div>
												<label className="block text-xs font-medium text-gray-700 mb-1">Name *</label>
												<input
													type="text"
													value={returnFormData['Permit Return_Authorizing Person (Permit Issuer)_Name'] || ''}
													onChange={(e) => handleReturnFormChange('Permit Return_Authorizing Person (Permit Issuer)_Name', e.target.value)}
													className="w-full px-2 py-1 text-sm border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
													required
												/>
											</div>
											<div>
												<label className="block text-xs font-medium text-gray-700 mb-1">Date *</label>
												<input
													type="date"
													value={returnFormData['Permit Return_Authorizing Person (Permit Issuer)_Date'] || ''}
													onChange={(e) => handleReturnFormChange('Permit Return_Authorizing Person (Permit Issuer)_Date', e.target.value)}
													className="w-full px-2 py-1 text-sm border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
													required
												/>
											</div>
											<div>
												<label className="block text-xs font-medium text-gray-700 mb-1">Time *</label>
												<input
													type="time"
													value={returnFormData['Permit Return_Authorizing Person (Permit Issuer)_Time'] || ''}
													onChange={(e) => handleReturnFormChange('Permit Return_Authorizing Person (Permit Issuer)_Time', e.target.value)}
													className="w-full px-2 py-1 text-sm border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
													required
												/>
											</div>
											<div>
												<label className="block text-xs font-medium text-gray-700 mb-1">Signature *</label>
												<div className="border border-gray-300 rounded-md p-2 text-center text-xs bg-gray-50 text-gray-500">
													Signature Pad
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>

							{/* People to Notify Section */}
							<div className="bg-white border border-gray-200 rounded-lg p-4">
								<h3 className="text-lg font-semibold text-gray-900 mb-3">People to Notify</h3>
								<div className="relative" ref={dropdownRef}>
									<label className="block text-sm font-medium text-gray-700 mb-2">Select users to notify</label>
									<button
										type="button"
										onClick={() => setIsNotifyDropdownOpen(!isNotifyDropdownOpen)}
										className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent bg-white text-left flex items-center justify-between"
									>
										<span className="text-sm text-gray-700">
											{selectedNotifyUsers.length === 0
												? "Select users to notify..."
												: `${selectedNotifyUsers.length} user(s) selected`
											}
										</span>
										<ChevronDown className={`h-4 w-4 text-gray-400 transition-transform ${isNotifyDropdownOpen ? 'rotate-180' : ''}`} />
									</button>

									{isNotifyDropdownOpen && (
										<div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto">
											{availableUsers.map((user) => (
												<label
													key={user.id}
													className="flex items-center px-3 py-2 hover:bg-gray-50 cursor-pointer"
												>
													<input
														type="checkbox"
														checked={selectedNotifyUsers.includes(user.id)}
														onChange={() => handleUserToggle(user.id)}
														className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded mr-3"
													/>
													<div className="flex-1">
														<div className="text-sm font-medium text-gray-900">{user.name}</div>
														<div className="text-xs text-gray-500">{user.role}</div>
													</div>
												</label>
											))}
										</div>
									)}
								</div>
							</div>

							{/* Action Buttons */}
							<div className="flex space-x-3">
								<button
									onClick={() => {
										setIsReturnModalOpen(false);
										setSelectedPermit(null);
										setReturnFormData({});
										setSelectedNotifyUsers([]);
										setIsNotifyDropdownOpen(false);
									}}
									className="flex-1 px-4 py-2 bg-gray-600 text-white rounded-md text-sm font-medium hover:bg-gray-700 transition-colors"
								>
									Cancel
								</button>
								<button
									onClick={handleConfirmReturn}
									className="flex-1 px-4 py-2 bg-green-600 text-white rounded-md text-sm font-medium hover:bg-green-700 transition-colors"
								>
									Return Permit
								</button>
							</div>
						</div>
					</div>
				</div>
			)}
		</div>
	);
};

export default ActivePermits;
