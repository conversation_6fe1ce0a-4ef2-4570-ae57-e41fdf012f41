import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  FileText, 
  Search, 
  Filter, 
  Calendar, 
  User, 
  MapPin,
  ChevronRight,
  Flame,
  HardHat,
  Zap,
  Mountain
} from 'lucide-react';

interface AllPermitsProps {
  siteId: string;
}

interface PermitItem {
  id: string;
  type: 'hot-work' | 'confined-space' | 'work-at-height' | 'excavation' | 'electrical' | 'general-work';
  title: string;
  description: string;
  serialNumber: string;
  status: 'active' | 'expired' | 'pending' | 'cancelled';
  createdDate: string;
  expiryDate: string;
  location: string;
  requestedBy: string;
  icon: React.ReactNode;
  color: string;
  route: string;
}

const AllPermits: React.FC<AllPermitsProps> = ({ siteId }) => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');

  // Mock permit data - replace with actual API call
  const permits: PermitItem[] = [
    {
      id: '1',
      type: 'hot-work',
      title: 'Hot Work Permit',
      description: 'Welding and cutting operations',
      serialNumber: 'HWP-2024-001',
      status: 'active',
      createdDate: '2024-01-15',
      expiryDate: '2024-01-16',
      location: 'Building A - Level 2',
      requestedBy: 'John Mwangi',
      icon: <Flame className="h-5 w-5" />,
      color: 'text-red-600',
      route: '/hot-work/form'
    },
    {
      id: '2',
      type: 'confined-space',
      title: 'Confined Space Entry Permit',
      description: 'Tank inspection and maintenance',
      serialNumber: 'CSE-2024-002',
      status: 'active',
      createdDate: '2024-01-15',
      expiryDate: '2024-01-16',
      location: 'Storage Tank Area',
      requestedBy: 'Sarah Njeri',
      icon: <Mountain className="h-5 w-5" />,
      color: 'text-blue-600',
      route: '/confined-space/form'
    },
    {
      id: '3',
      type: 'work-at-height',
      title: 'Work at Height Permit',
      description: 'Roof maintenance and repairs',
      serialNumber: 'WAH-2024-003',
      status: 'pending',
      createdDate: '2024-01-15',
      expiryDate: '2024-01-16',
      location: 'Main Building Roof',
      requestedBy: 'Peter Kamau',
      icon: <HardHat className="h-5 w-5" />,
      color: 'text-orange-600',
      route: '/work-at-height/form'
    },
    {
      id: '4',
      type: 'excavation',
      title: 'Excavation Permit',
      description: 'Foundation digging operations',
      serialNumber: 'EXC-2024-004',
      status: 'expired',
      createdDate: '2024-01-14',
      expiryDate: '2024-01-15',
      location: 'Site Entrance',
      requestedBy: 'Mary Wanjiku',
      icon: <Mountain className="h-5 w-5" />,
      color: 'text-yellow-600',
      route: '/excavation/form'
    },
    {
      id: '5',
      type: 'electrical',
      title: 'Electrical Work Permit',
      description: 'Power line installation',
      serialNumber: 'EWP-2024-005',
      status: 'active',
      createdDate: '2024-01-15',
      expiryDate: '2024-01-16',
      location: 'Electrical Room',
      requestedBy: 'David Ochieng',
      icon: <Zap className="h-5 w-5" />,
      color: 'text-purple-600',
      route: '/electrical/form'
    },
    {
      id: '6',
      type: 'general-work',
      title: 'General Work Permit',
      description: 'General permit to work form',
      serialNumber: 'PTW-2024-006',
      status: 'active',
      createdDate: '2024-01-15',
      expiryDate: '2024-01-16',
      location: 'General Work Area',
      requestedBy: 'James Kiprotich',
      icon: <FileText className="h-5 w-5" />,
      color: 'text-green-600',
      route: '/ptw/form'
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'expired':
        return 'bg-red-100 text-red-800';
      case 'cancelled':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const filteredPermits = permits.filter(permit => {
    const matchesSearch = 
      permit.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      permit.serialNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      permit.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      permit.requestedBy.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesType = filterType === 'all' || permit.type === filterType;
    const matchesStatus = filterStatus === 'all' || permit.status === filterStatus;
    
    return matchesSearch && matchesType && matchesStatus;
  });

  const handlePermitClick = (permit: PermitItem) => {
    if (siteId) {
      navigate(`/sites/${siteId}${permit.route}/${permit.id}`);
    } else {
      navigate(`${permit.route}/${permit.id}`);
    }
  };

  const permitTypes = [
    { value: 'all', label: 'All Types' },
    { value: 'hot-work', label: 'Hot Work' },
    { value: 'confined-space', label: 'Confined Space' },
    { value: 'work-at-height', label: 'Work at Height' },
    { value: 'excavation', label: 'Excavation' },
    { value: 'electrical', label: 'Electrical' }
  ];

  const statusOptions = [
    { value: 'all', label: 'All Status' },
    { value: 'active', label: 'Active' },
    { value: 'pending', label: 'Pending' },
    { value: 'expired', label: 'Expired' },
    { value: 'cancelled', label: 'Cancelled' }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">All Permits</h2>
          <p className="text-sm text-gray-600 mt-1">
            Complete list of all permit forms ({filteredPermits.length} of {permits.length})
          </p>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white p-4 rounded-lg border border-gray-200">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search permits..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
            />
          </div>

          {/* Type Filter */}
          <div className="relative">
            <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent appearance-none"
            >
              {permitTypes.map(type => (
                <option key={type.value} value={type.value}>{type.label}</option>
              ))}
            </select>
          </div>

          {/* Status Filter */}
          <div>
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
            >
              {statusOptions.map(status => (
                <option key={status.value} value={status.value}>{status.label}</option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Permits List */}
      {filteredPermits.length > 0 ? (
        <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
          <div className="divide-y divide-gray-200">
            {filteredPermits.map((permit) => (
              <div
                key={permit.id}
                className="p-6 hover:bg-gray-50 cursor-pointer transition-colors group"
                onClick={() => handlePermitClick(permit)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4 flex-1">
                    {/* Icon */}
                    <div className={`flex-shrink-0 ${permit.color}`}>
                      {permit.icon}
                    </div>

                    {/* Content */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-3 mb-2">
                        <h3 className="text-lg font-semibold text-gray-900 group-hover:text-green-600 transition-colors">
                          {permit.title}
                        </h3>
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(permit.status)}`}>
                          {permit.status.charAt(0).toUpperCase() + permit.status.slice(1)}
                        </span>
                      </div>
                      
                      <p className="text-sm text-gray-600 mb-2">{permit.description}</p>
                      
                      <div className="flex items-center space-x-6 text-sm text-gray-500">
                        <div className="flex items-center space-x-1">
                          <FileText className="h-4 w-4" />
                          <span>{permit.serialNumber}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <MapPin className="h-4 w-4" />
                          <span>{permit.location}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <User className="h-4 w-4" />
                          <span>{permit.requestedBy}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Calendar className="h-4 w-4" />
                          <span>Expires: {new Date(permit.expiryDate).toLocaleDateString()}</span>
                        </div>
                      </div>
                    </div>

                    {/* Arrow */}
                    <div className="flex-shrink-0">
                      <ChevronRight className="h-5 w-5 text-gray-400 group-hover:text-gray-600 transition-colors" />
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      ) : (
        <div className="text-center py-12">
          <FileText className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No permits found</h3>
          <p className="mt-1 text-sm text-gray-500">
            {searchTerm || filterType !== 'all' || filterStatus !== 'all'
              ? 'Try adjusting your search or filter criteria'
              : 'No permits have been created yet'}
          </p>
        </div>
      )}
    </div>
  );
};

export default AllPermits;
