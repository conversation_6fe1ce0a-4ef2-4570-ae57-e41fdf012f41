import { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { AuthProvider } from './hooks/useAuthContext';
import { TenantProvider } from './hooks/useTenantContext.tsx';
import ProtectedRoute from './components/auth/ProtectedRoute';
import Sidebar from './components/layout/Sidebar';
import HelpButton from './components/layout/HelpButton';
import Dashboard from './pages/Dashboard';
import DataPage from './pages/DataPage';
import Settings from './pages/Settings';
import SiteDashboard from './pages/SiteDashboard';
import WorkerDirectory from './pages/WorkerDirectory';
import WorkerProfile from './pages/WorkerProfile';
import TimeManagement from './pages/TimeManagement';
import CompanyReports from './pages/CompanyReports';
import TrainingPage from './pages/TrainingPage';
import TasksPage from './pages/TasksPage';
import TaskDetailPage from './pages/TaskDetailPage';
import TaskCreatePage from './pages/TaskCreatePage';
import TaskRequestPage from './pages/TaskRequestPage';
import PermitsPage from './pages/PermitsPage';
import InspectionsPage from './pages/InspectionsPage';
import SafetyPage from './pages/SafetyPage';
import EquipmentPage from './pages/EquipmentPage';
import PPEPage from './pages/PPEPage';
import WeatherPage from './pages/WeatherPage';
import DocumentsPage from './pages/DocumentsPage';
import SiteDocumentsPage from './pages/SiteDocumentsPage';
import FormsPage from './pages/FormsPage';
import LoginPage from './pages/LoginPage';
import RegisterPage from './pages/RegisterPage';
import ForgotPasswordPage from './pages/ForgotPasswordPage';
import ResetPasswordPage from './pages/ResetPasswordPage';
import AccountPage from './pages/AccountPage';
import PageNotFound from './pages/PageNotFound';
import NewTrainingForm from "./components/training/NewTrainingForm";
import ToolboxPage from './pages/ToolboxPage.tsx';
import SiteInfoPage from './pages/SiteInfoPage';
import NotificationsPage from './pages/NotificationsPage';
import StandaloneObservationPage from './pages/StandaloneObservationPage';
import CreateWorkerPage from './pages/CreateWorkerPage';
import WorkerFormDemoPage from './pages/WorkerFormDemoPage';
import InspectionFormPage from './pages/InspectionFormPage';
import InspectionFormDemoPage from './pages/InspectionFormDemoPage';
import InspectionFormsListPage from './pages/InspectionFormsListPage';
import PTWFormPage from './pages/PTWFormPage';
import PTWFormDemoPage from './pages/PTWFormDemoPage';
import ConfinedSpaceFormPage from './pages/ConfinedSpaceFormPage';
import ConfinedSpaceFormDemoPage from './pages/ConfinedSpaceFormDemoPage';
import ExcavationFormPage from './pages/ExcavationFormPage';
import ExcavationFormDemoPage from './pages/ExcavationFormDemoPage';
import WorkAtHeightFormPage from './pages/WorkAtHeightFormPage';
import WorkAtHeightFormDemoPage from './pages/WorkAtHeightFormDemoPage';
import HotWorkFormPage from './pages/HotWorkFormPage';
import HotWorkFormDemoPage from './pages/HotWorkFormDemoPage';
import ApprovePermitPage from './pages/permits/ApprovePermitPage';
import './index.css';

export default function App() {
	useEffect(() => {
		// Load Inter font
		const link = document.createElement("link");
		link.href =
			"https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap";
		link.rel = "stylesheet";
		document.head.appendChild(link);

		return () => {
			// Clean up
			document.head.removeChild(link);
		};
	}, []);

  return (
    <AuthProvider>
      <TenantProvider defaultTenantId="tenant-1">
        <Router>
          <Routes>
            {/* Public Routes */}
            <Route path="/login" element={<LoginPage />} />
            <Route path="/register" element={<RegisterPage />} />
            <Route path="/forgot-password" element={<ForgotPasswordPage />} />
            <Route path="/reset-password" element={<ResetPasswordPage />} />

            {/* Standalone External Routes */}
            <Route path="/sites/:siteId/observation" element={<StandaloneObservationPage />} />

            {/* Protected Routes */}
            <Route path="/*" element={
              <ProtectedRoute>
                <div className="bg-[#f3f2ee] min-h-screen font-[Inter]">
                  <Sidebar />
                  <main className="min-h-screen">
                    <Routes>
                      <Route path="/" element={<Dashboard />} />
                      <Route path="/data" element={<DataPage />} />
                      <Route path="/settings" element={<Settings />} />
                      <Route path="/account" element={<AccountPage />} />
                      <Route path="/notifications" element={<NotificationsPage />} />
                      <Route path="/documents" element={<DocumentsPage />} />
                      <Route path="/company-reports" element={<CompanyReports />} />
                      <Route path="/workers/create" element={<CreateWorkerPage />} />
                      <Route path="/demo/worker-form" element={<WorkerFormDemoPage />} />
                      <Route path="/demo/inspection-form" element={<InspectionFormDemoPage />} />
                      <Route path="/demo/ptw-form" element={<PTWFormDemoPage />} />
                      <Route path="/demo/confined-space-form" element={<ConfinedSpaceFormDemoPage />} />
                      <Route path="/demo/excavation-form" element={<ExcavationFormDemoPage />} />
                      <Route path="/demo/work-at-height-form" element={<WorkAtHeightFormDemoPage />} />
                      <Route path="/demo/hot-work-form" element={<HotWorkFormDemoPage />} />
                      <Route path="/inspections/form" element={<InspectionFormsListPage />} />
                      <Route path="/inspections/form/:id" element={<InspectionFormPage />} />
                      <Route path="/ptw/form/:id" element={<PTWFormPage />} />
                      <Route path="/ptw-form" element={<PTWFormPage />} />
                      <Route path="/confined-space/form/:id" element={<ConfinedSpaceFormPage />} />
                      <Route path="/confined-space/form" element={<ConfinedSpaceFormPage />} />
                      <Route path="/excavation/form/:id" element={<ExcavationFormPage />} />
                      <Route path="/excavation/form" element={<ExcavationFormPage />} />
                      <Route path="/work-at-height/form/:id" element={<WorkAtHeightFormPage />} />
                      <Route path="/work-at-height/form" element={<WorkAtHeightFormPage />} />
                      <Route path="/hot-work/form/:id" element={<HotWorkFormPage />} />
                      <Route path="/hot-work/form" element={<HotWorkFormPage />} />
                      <Route path="/sites/:siteId/dashboard" element={<SiteDashboard />} />
                      <Route path="/sites/:siteId/workers" element={<WorkerDirectory />} />
                      <Route path="/sites/:siteId/workers/new" element={<CreateWorkerPage />} />
                      <Route path="/sites/:siteId/workers/create" element={<CreateWorkerPage />} />
                      <Route path="/sites/:siteId/workers/:workerId" element={<WorkerProfile />} />
                      <Route path="/sites/:siteId/time" element={<TimeManagement />} />
                      <Route path="/sites/:siteId/training" element={<TrainingPage />} />
					            <Route path="/sites/:siteId/training/new"	element={<NewTrainingForm />} />
                      <Route path="/sites/:siteId/tasks" element={<TasksPage />} />
                      <Route path="/sites/:siteId/tasks/create" element={<TaskCreatePage />} />
                      <Route path="/sites/:siteId/tasks/request/:taskId" element={<TaskRequestPage />} />
                      <Route path="/sites/:siteId/tasks/:taskId" element={<TaskDetailPage />} />
                      <Route path="/sites/:siteId/tasks/*" element={<TasksPage />} />
                      <Route path="/sites/:siteId/permits" element={<PermitsPage />} />
                      <Route path="/sites/:siteId/permits/approve" element={<ApprovePermitPage />} />
                      <Route path="/sites/:siteId/inspections" element={<InspectionsPage />} />
                      <Route path="/sites/:siteId/inspections/form" element={<InspectionFormsListPage />} />
                      <Route path="/sites/:siteId/inspections/form/:id" element={<InspectionFormPage />} />
                      <Route path="/sites/:siteId/ptw-form" element={<PTWFormPage />} />
                      <Route path="/sites/:siteId/ptw-form/:id" element={<PTWFormPage />} />
                      <Route path="/sites/:siteId/confined-space/form/:id" element={<ConfinedSpaceFormPage />} />
                      <Route path="/sites/:siteId/confined-space/form" element={<ConfinedSpaceFormPage />} />
                      <Route path="/sites/:siteId/excavation/form/:id" element={<ExcavationFormPage />} />
                      <Route path="/sites/:siteId/excavation/form" element={<ExcavationFormPage />} />
                      <Route path="/sites/:siteId/work-at-height/form/:id" element={<WorkAtHeightFormPage />} />
                      <Route path="/sites/:siteId/work-at-height/form" element={<WorkAtHeightFormPage />} />
                      <Route path="/sites/:siteId/hot-work/form/:id" element={<HotWorkFormPage />} />
                      <Route path="/sites/:siteId/hot-work/form" element={<HotWorkFormPage />} />
                      <Route path="/sites/:siteId/safety" element={<SafetyPage />} />
                      <Route path="/sites/:siteId/equipment" element={<EquipmentPage />} />
                      <Route path="/sites/:siteId/ppe" element={<PPEPage />} />
                      <Route path="/sites/:siteId/weather" element={<WeatherPage />} />
                      <Route path="/sites/:siteId/documents" element={<SiteDocumentsPage />} />
                      <Route path="/sites/:siteId/forms" element={<FormsPage />} />
                      <Route path="/sites/:siteId/toolbox" element={<ToolboxPage />} />
                      <Route path="/sites/:siteId/info" element={<SiteInfoPage />} />
                      <Route path="*" element={<PageNotFound />} />
                    </Routes>
                  </main>
                  <HelpButton />
                </div>
              </ProtectedRoute>
            } />
          </Routes>
        </Router>
      </TenantProvider>
    </AuthProvider>
  );
}
