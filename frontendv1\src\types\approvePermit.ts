export interface File {
  id: string;
  name: string;
}

export interface Folder {
  id: string;
  name: string;
  files: File[];
  folders: Folder[];
}

export interface ApprovePermitComponentProps {
  folders: Folder[];
  showButtons: boolean;
  onFileClick?: (fileId: string, filePath: string, fileName: string) => void;
  onDateClick?: (date: Date) => void;
  documentTitle?: string;
  documentChildren?: React.ReactNode;
  extraDetailsChildren?: React.ReactNode;
}
